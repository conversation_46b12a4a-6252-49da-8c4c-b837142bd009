{"permissions": {"allow": ["Bash(cd \"/Users/<USER>/Downloads/Github/fillify/wxt\")", "Bash(npm run build)", "Bash(npm run lint)", "Bash(npm run type-check)", "Bash(npm run dev)", "Bash(grep -n \"console.error\\|throw\\|Error\" /Users/<USER>/Downloads/Github/fillify/wxt/.output/chrome-mv3/content-scripts/content.js)", "Bash(find /Users/<USER>/Downloads/Github/fillify/wxt -name \"*inject*\" -type f)", "Bash(find node_modules -name \"*.d.ts\" -path \"*/wxt/*\")", "Bash(find node_modules -name \"*.d.ts\" -path \"*/wxt/client*\")", "Bash(find node_modules -name \"*client*\" -path \"*/wxt/*\")", "Bash(ls node_modules/wxt/dist/client/)", "Bash(ls node_modules/wxt/dist/client/content-scripts/)", "Bash(ls node_modules/wxt/dist/client/content-scripts/ui/)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/src/services/reasoning-models.ts)", "Bash(npm run typecheck)", "Bash(npm run)", "Bash(pkill -f \"wxt\")", "Bash(npm run compile)", "Bash(rm -rf /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/injected-popup)", "Bash(rm -rf /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/components)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/index.html)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/main.ts)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/App.vue)", "Bash(grep -n \"PROVIDER_MODELS\\[\" /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/background.ts)", "Bash(grep -n \"STORAGE_KEYS\\.\" /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/content.ts)", "Bash(grep -r \"onboarding.js\" /Users/<USER>/Downloads/Github/fillify/wxt/.output/)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/onboarding.html)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/onboarding/onboarding.js)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/popup/popup.css)", "Bash(rm /Users/<USER>/Downloads/Github/fillify/wxt/entrypoints/settings/vite-env.d.ts)", "Bash(node -e \"\n// 测试当前get-user接口返回的数据结构\nconst testGetUser = async () => {\n  try {\n    // 模拟当前的fetchUserInfo调用\n    const response = await fetch(''https://fillify-343190162770.asia-east1.run.app/api/users/get-user'', {\n      method: ''POST'',\n      headers: {\n        ''Content-Type'': ''application/json'',\n      },\n      body: JSON.stringify({ userId: ''af74f572-db3f-4cef-ae1d-561623816d0d'' })\n    })\n    \n    const data = await response.json()\n    console.log(''get-user response:'', JSON.stringify(data, null, 2))\n    \n    if (data.user && data.user.token) {\n      console.log(''JWT token found in response:'', data.user.token.substring(0, 50) + ''...'')\n    } else {\n      console.log(''No JWT token found in user data'')\n      console.log(''Available user fields:'', Object.keys(data.user || {}))\n    }\n  } catch (error) {\n    console.error(''Test failed:'', error.message)\n  }\n}\n\ntestGetUser()\n\")", "Bash(node test-jwt-auth.js)", "<PERSON><PERSON>(rm test-jwt-auth.js)", "Bash(git add entrypoints/popup/App.vue src/components/InPagePopup.ts)", "Bash(npm install cross-env --save-dev)", "Bash(npm run build:dev)", "Bash(grep -r \"console\\.\\(log\\|warn\\|error\\|info\\|debug\\)\" .output/chrome-mv3/)", "Bash(grep -c \"console\\.\\(log\\|warn\\|error\\|info\\|debug\\)\" .output/chrome-mv3/background.js)", "Bash(grep -c \"console\\.\\(log\\|warn\\|error\\|info\\|debug\\)\" .output/chrome-mv3/background.js)", "WebFetch(domain:wxt.dev)", "Bash(rm -rf /Users/<USER>/Downloads/Github/fillify/wxt/src/core/)"], "deny": []}}
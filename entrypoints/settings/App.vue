<template>
  <div class="app-container">
    <div v-if="isLoading" class="loading-overlay">
      <div class="page-spinner" role="status" aria-label="Loading"></div>
    </div>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <img src="/icon/icon48.png" alt="Fillify Logo" class="logo">
        <h1>Fillify Settings</h1>
      </div>
      <nav class="sidebar-nav">
        <button
          class="nav-item"
          :class="{ active: activeTab === 'general' }"
          @click="switchTab('general')"
          data-tab="general"
        >
          General
        </button>
        <button
          class="nav-item"
          :class="{ active: activeTab === 'library' }"
          @click="switchTab('library')"
          data-tab="library"
        >
          Library
        </button>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <div class="content-wrapper">
        <!-- General Tab Content -->
        <div class="tab-content" :class="{ active: activeTab === 'general' }" id="general-tab">
          <!-- Quick Actions Card -->
          <div class="settings-card">
            <div class="card-header">
              <h2>Quick Actions</h2>
            </div>
            <div class="card-content">
              <div class="quick-actions">
                <div class="action-item">
                  <div class="action-info">
                    <h3>Onboarding Guide</h3>
                    <p>Review setup steps and quick tips</p>
                  </div>
                  <button id="show-onboarding" class="button-secondary" @click="showOnboarding">
                    Open Guide
                  </button>
                </div>
                <div
                  class="action-item"
                  v-if="userFeatures.showCustomApiToggle && !isLoadingFeatures"
                >
                  <div class="action-info">
                    <h3>Use Custom API</h3>
                    <p>Use your own API keys instead of Fillify's shared API</p>
                  </div>
                  <label class="switch">
                    <input
                      type="checkbox"
                      id="use-custom-api"
                      v-model="settings.useCustomApi"
                      @change="() => saveSettings()"
                    >
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Token Usage Card -->
          <div class="settings-card" id="token-usage">
            <div class="card-header">
              <h2>Token Usage</h2>
            </div>
            <div class="card-content">
              <div class="token-grid">
                <!-- Token cards will be added dynamically -->
                <div v-for="(stats, provider) in tokenStats" :key="provider" class="token-card">
                  <div class="token-header">
                    <h3>{{ provider }}</h3>
                  </div>
                  <div class="token-stats">
                    <div class="stat-item">
                      <span class="stat-label">Prompt Tokens</span>
                      <span class="stat-value">{{ stats.promptTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Completion Tokens</span>
                      <span class="stat-value">{{ stats.completionTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Total Tokens</span>
                      <span class="stat-value">{{ stats.totalTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item last-updated" v-if="stats.lastUpdated">
                      <span class="stat-label">Last Updated</span>
                      <span class="stat-value">{{ formatDate(stats.lastUpdated) }}</span>
                    </div>
                  </div>
                </div>
                <!-- 如果没有数据，显示提示信息 -->
                <div v-if="Object.keys(tokenStats).length === 0" class="no-data-message">
                  No token usage data available yet.
                </div>
              </div>
              <button
                id="reset-stats"
                class="button-danger"
                @click="resetStats"
                v-if="Object.keys(tokenStats).length > 0"
                title="This will delete all token usage statistics records"
              >
                <svg viewBox="0 0 24 24" class="button-icon" width="16" height="16">
                  <path d="M17.65 6.35A7.958 7.958 0 0012 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0112 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                Reset Statistics
              </button>
            </div>
          </div>

          <!-- AI Providers Card -->
          <div class="settings-card" id="ai-providers">
            <div class="card-header">
              <h2>AI Providers</h2>
            </div>
            <div class="card-content">
              <div class="providers-grid">
                <div v-for="provider in providers" :key="provider.id" class="provider-card">
                  <div class="provider-header">
                    <h3>{{ provider.name }}</h3>
                    <!-- Ollama连接状态指示器 -->
                    <div v-if="provider.id === 'ollama'" class="ollama-status">
                      <div 
                        class="status-dot" 
                        :class="{ 
                          'online': ollamaStatus === 'online', 
                          'offline': ollamaStatus === 'offline',
                          'checking': ollamaStatus === 'checking'
                        }"
                        :title="getOllamaStatusText()"
                      ></div>
                      <span class="status-text">{{ getOllamaStatusText() }}</span>
                    </div>
                    <button
                      v-if="apiKeys[provider.id] && !validatedKeys[provider.id]"
                      class="provider-refresh-button"
                      @click="validateApiKey(provider.id)"
                      title="Refresh API Key"
                      :disabled="validating[provider.id]"
                    >
                      <svg viewBox="0 0 24 24" class="refresh-icon" :class="{ 'rotating': validating[provider.id] }">
                        <path d="M17.65 6.35A7.958 7.958 0 0012 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0112 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                      </svg>
                    </button>
                  </div>
                  <div class="key-input-group" style="position: relative; display: flex; align-items: center; width: 100%; margin-top: 0.5rem;">
                    <input
                      :type="provider.id === 'ollama' ? 'text' : 'password'"
                      :id="`${provider.id}-key`"
                      class="key-input"
                      :class="{
                        'validating': validating[provider.id],
                        'valid': validatedKeys[provider.id],
                        'invalid': apiKeys[provider.id] && !validatedKeys[provider.id]
                      }"
                      :placeholder="provider.id === 'ollama' ? 'Enter Ollama endpoint (e.g., http://localhost:11434)' : `Enter ${provider.name} API key`"
                      v-model="apiKeys[provider.id]"
                      @input="handleApiKeyInput(provider.id)"
                      style="flex: 1; width: 100%; padding: 0.5rem 70px 0.5rem 0.75rem; border: 1px solid var(--border-color); border-radius: var(--radius-md); font-size: 0.9rem; transition: border-color 0.2s; height: 38px;"
                    >
                    <div
                      v-if="validating[provider.id]"
                      class="loading-spinner"
                      style="position: absolute; right: 40px; top: 50%; transform: translateY(-50%); width: 16px; height: 16px; border: 2px solid #f3f3f3; border-top: 2px solid var(--primary-color); border-radius: 50%; animation: spin 1s linear infinite; z-index: 2;"
                    ></div>
                    <!-- 只为非Ollama提供商显示密码可见性切换按钮 -->
                    <button
                      v-if="provider.id !== 'ollama'"
                      class="visibility-toggle"
                      @click="toggleVisibility(provider.id)"
                      style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; padding: 4px; display: flex; align-items: center; justify-content: center; z-index: 3;">
                      <svg viewBox="0 0 24 24" class="eye-icon" style="width: 20px; height: 20px; fill: var(--text-secondary);">
                        <path v-if="!showPassword[provider.id]" d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        <path v-else d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
                      </svg>
                    </button>
                  </div>
                  <div class="api-key-help">
                    <a :href="PROVIDER_LINKS[provider.id]" target="_blank">
                      {{ provider.id === 'ollama' ? 'Download Ollama' : 'Get API Key' }}
                    </a>
                  </div>
                </div>
              </div>

              <div class="provider-settings">
                <div class="settings-grid">
                  <!-- Default Provider Setting -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <h4>Default Provider</h4>
                      <p>Select your preferred AI provider</p>
                    </div>
                    <CustomSelect
                      id="default-provider"
                      :modelValue="settings.defaultProvider"
                      :options="providerOptions"
                      placeholder="Select Default Provider"
                      @update:modelValue="(value) => { settings.defaultProvider = value; updateModelOptions(); }"
                      width="240px"
                    />
                  </div>

                  <!-- Default Model Setting -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <h4>Default Model</h4>
                      <p>Select the default model for the chosen provider</p>
                    </div>
                    <ModelSelect
                      v-model="settings.defaultModel"
                      :models="availableModels"
                      :is-loading="isLoadingModels"
                    />
                  </div>

                  <!-- Show Reasoning Content Setting -->
                  <!-- Custom API Toggle (只在后端授权时显示) -->

                  <div class="setting-item">
                    <div class="setting-info">
                      <h4>Show Reasoning Content</h4>
                      <p>Display AI reasoning process in a bubble for supported models</p>
                    </div>
                    <label class="switch">
                      <input
                        type="checkbox"
                        id="show-reasoning-content"
                        v-model="settings.showReasoningContent"
                        @change="() => saveSettings()"
                      >
                      <span class="slider"></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Library Tab Content -->
        <div class="tab-content" :class="{ active: activeTab === 'library' }" id="library-tab">
          <!-- Projects Card -->
          <div class="settings-card">
            <div class="card-header">
              <h2>Bug Report Projects</h2>
              <button class="add-project-button" @click="openProjectModal">
                <svg viewBox="0 0 24 24" class="nav-icon">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
                Add Project
              </button>
            </div>
            <div class="card-content">
              <div class="setting-item">
                <div class="setting-info">
                  <h4>Show Project Details</h4>
                  <p>Display additional information for each project</p>
                </div>
                <label class="switch">
                  <input
                    type="checkbox"
                    id="show-project-details"
                    v-model="settings.showProjectDetails"
                    @change="() => saveSettings()"
                  >
                  <span class="slider"></span>
                </label>
              </div>

              <div class="projects-grid">
                <div
                  v-for="project in visibleProjects"
                  :key="project.id"
                  class="project-card"
                >
                  <div class="project-header">
                    <h3>{{ project.name }}</h3>
                    <div class="project-actions">
                      <button class="project-action-button edit-button" @click="editProject(project)" title="Edit Project">
                        <svg viewBox="0 0 24 24" class="nav-icon">
                          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                      </button>
                      <button class="project-action-button delete-button" @click="deleteProject(project.id)" title="Delete Project">
                        <svg viewBox="0 0 110.61 122.88" class="nav-icon">
                          <path d="M39.27,58.64a4.74,4.74,0,1,1,9.47,0V93.72a4.74,4.74,0,1,1-9.47,0V58.64Zm63.6-19.86L98,103a22.29,22.29,0,0,1-6.33,14.1,19.41,19.41,0,0,1-13.88,5.78h-45a19.4,19.4,0,0,1-13.86-5.78l0,0A22.31,22.31,0,0,1,12.59,103L7.74,38.78H0V25c0-3.32,1.63-4.58,4.84-4.58H27.58V10.79A10.82,10.82,0,0,1,38.37,0H72.24A10.82,10.82,0,0,1,83,10.79v9.62h23.35a6.19,6.19,0,0,1,1,.06A3.86,3.86,0,0,1,110.59,24c0,.2,0,.38,0,.57V38.78Zm-9.5.17H17.24L22,102.3a12.82,12.82,0,0,0,3.57,8.1l0,0a10,10,0,0,0,7.19,3h45a10.06,10.06,0,0,0,7.19-3,12.8,12.8,0,0,0,3.59-8.1L93.37,39ZM71,20.41V12.05H39.64v8.36ZM61.87,58.64a4.74,4.74,0,1,1,9.47,0V93.72a4.74,4.74,0,1,1-9.47,0V58.64Z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div v-if="settings.showProjectDetails" class="project-details">
                    <div class="detail-item">
                      <span class="detail-label">Description</span>
                      <p class="detail-content">{{ project.description || 'No description provided' }}</p>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">Environment</span>
                      <p class="detail-content">{{ project.environment || 'No environment information' }}</p>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">Template</span>
                      <p class="detail-content">{{ project.template || 'No template provided' }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="projects.length > (currentPage + 1) * projectsPerPage"
                class="load-more"
                @click="currentPage++"
              >
                加载更多
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Project Modal -->
  <div id="project-modal" class="modal" v-show="showProjectModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ editingProject ? 'Edit Project' : 'Add Project' }}</h3>
        <button class="modal-close" @click="closeProjectModal">&times;</button>
      </div>
      <div class="modal-body">
        <form class="modal-form" @submit.prevent="saveProject">
          <div class="form-group">
            <label for="project-name">Project Name</label>
            <input
              type="text"
              id="project-name"
              v-model="projectForm.name"
              placeholder="Enter project name"
              required
            >
          </div>
          <div class="form-group">
            <label for="project-description">Description</label>
            <textarea
              id="project-description"
              v-model="projectForm.description"
              placeholder="Enter project description"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="project-environment">Environment</label>
            <textarea
              id="project-environment"
              v-model="projectForm.environment"
              placeholder="Enter environment information"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="project-template">Bug Report Template</label>
            <textarea
              id="project-template"
              v-model="projectForm.template"
              placeholder="Enter bug report template"
            ></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="button-secondary" @click="closeProjectModal">Cancel</button>
        <button class="button-primary" @click="saveProject">Save Project</button>
      </div>
    </div>
  </div>

  <!-- Notification Component -->
  <div
    v-if="notification.show"
    class="notification"
    :class="notification.type"
    @click="hideNotification"
  >
    {{ notification.message }}
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { STORAGE_KEYS, PROVIDER_MODELS } from './constants'
import type { Provider, Settings, Project, ProjectForm, Notification, TokenStats, UserFeatureFlags } from './types'
import {
  fetchOpenAIModels,
  fetchClaudeModels,
  fetchMoonshotModels,
  fetchGeminiModels,
  fetchDeepSeekModels,
  fetchOpenRouterModels,
  fetchOllamaModels,
  Model
} from '../../src/services/ai-service'
import ModelSelect from './components/ModelSelect.vue'
import CustomSelect from './components/CustomSelect.vue'
import { debounce, handleError, fetchUserFeatureFlags } from './utils'

// Provider Links
const PROVIDER_LINKS: Record<string, string> = {
  openai: 'https://platform.openai.com/api-keys',
  moonshot: 'https://platform.moonshot.cn/console/api-keys',
  claude: 'https://console.anthropic.com/account/keys',
  gemini: 'https://makersuite.google.com/app/apikey',
  deepseek: 'https://platform.deepseek.com/api_keys',
  openrouter: 'https://openrouter.ai/keys',
  ollama: 'https://ollama.com/download'
}

// 状态管理
const activeTab = ref('general')
const showProjectModal = ref(false)
const isLoadingModels = ref(false)
const providers = ref<Provider[]>([
  { id: 'openai', name: 'OpenAI' },
  { id: 'claude', name: 'Claude' },
  { id: 'moonshot', name: 'Moonshot' },
  { id: 'gemini', name: 'Gemini' },
  { id: 'deepseek', name: 'DeepSeek' },
  { id: 'openrouter', name: 'OpenRouter' },
  { id: 'ollama', name: 'Ollama' }
])
const apiKeys = reactive<Record<string, string>>({})
const validating = reactive<Record<string, boolean>>({})
const validatedKeys = reactive<Record<string, boolean>>({})
const showPassword = reactive<Record<string, boolean>>({})
const settings = reactive<Settings>({
  useCustomApi: true, // 默认使用Custom API
  defaultProvider: '',
  defaultModel: '',
  showProjectDetails: false,
  showReasoningContent: true // 默认开启推理内容显示
})
const tokenStats = reactive<Record<string, TokenStats>>({})
const projects = ref<Project[]>([])
const availableModels = ref<Model[]>([])

// 项目表单状态
const projectForm = reactive<ProjectForm>({
  name: '',
  description: '',
  environment: '',
  template: ''
})

const editingProject = ref<Project | null>(null)

// 添加通知状态
const notification = reactive<Notification>({
  show: false,
  message: '',
  type: 'success',
  timer: undefined
})

// 功能标志状态
const userFeatures = reactive<UserFeatureFlags>({
  showCustomApiToggle: false
})
const isLoadingFeatures = ref(false)

// 添加加载状态
const isLoading = ref(false)

// 添加Ollama状态跟踪
const ollamaStatus = ref<'online' | 'offline' | 'checking'>('offline')

// 添加项目列表虚拟滚动
const projectsPerPage = 10
const currentPage = ref(0)

const visibleProjects = computed(() => {
  const start = currentPage.value * projectsPerPage
  return projects.value.slice(start, start + projectsPerPage)
})

// 为自定义下拉菜单准备的选项
const providerOptions = computed(() => {
  return [
    ...providers.value.map(provider => ({
      value: provider.id,
      label: provider.name,
      disabled: !validatedKeys[provider.id] || (provider.id === 'ollama' && ollamaStatus.value === 'offline')
    }))
  ]
})

// Ollama默认地址
const OLLAMA_DEFAULT_ENDPOINT = 'http://localhost:11434'

// 获取Ollama状态文本
const getOllamaStatusText = () => {
  switch (ollamaStatus.value) {
    case 'online': return 'Online'
    case 'offline': return 'Offline'
    case 'checking': return 'Checking...'
    default: return 'Unknown'
  }
}

// 检查Ollama连接状态
const checkOllamaConnection = async (endpoint?: string) => {
  const ollamaEndpoint = endpoint || apiKeys.ollama || OLLAMA_DEFAULT_ENDPOINT
  ollamaStatus.value = 'checking'
  
  try {
    const response = await fetch(`${ollamaEndpoint}/v1/models`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ollama'
      },
      signal: AbortSignal.timeout(5000) // 5秒超时
    })

    if (response.ok) {
      ollamaStatus.value = 'online'
      console.log('[Settings] Ollama is online:', ollamaEndpoint)
      return true
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    ollamaStatus.value = 'offline'
    console.log('[Settings] Ollama is offline:', error)
    
    // 如果Ollama掉线且是当前默认提供商，自动切换到其他可用提供商
    if (settings.defaultProvider === 'ollama') {
      const availableProviders = Object.keys(validatedKeys).filter(key => 
        validatedKeys[key] && key !== 'ollama'
      )
      if (availableProviders.length > 0) {
        settings.defaultProvider = availableProviders[0]
        showNotification('Ollama is offline, switched to ' + providers.value.find(p => p.id === availableProviders[0])?.name, 'error')
      } else {
        settings.defaultProvider = ''
        settings.defaultModel = ''
        showNotification('Ollama is offline and no other providers available', 'error')
      }
    }
    return false
  }
}

// 方法定义
const switchTab = (tab: string) => {
  activeTab.value = tab
  chrome.storage.sync.set({ [STORAGE_KEYS.ACTIVE_TAB]: tab })
}

const showOnboarding = () => {
  chrome.tabs.create({
    url: 'onboarding.html'
  })
}

const resetStats = async () => {
  // 添加确认对话框
  if (!confirm('Are you sure you want to delete all token usage statistics? This action cannot be undone.')) {
    return
  }

  try {
    // 完全删除所有token统计记录，而不是设置为零
    // 保存空对象以删除所有统计数据
    const emptyStats = {}

    // 更新本地状态
    Object.keys(tokenStats).forEach(key => {
      delete tokenStats[key]
    })

    // 保存空对象到存储中，这将删除所有token统计记录
    await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: emptyStats })

    // 显示成功通知
    showNotification('Token usage statistics deleted successfully', 'success')

    console.log('Token statistics reset successfully - all records deleted')
  } catch (error) {
    console.error('Error resetting token statistics:', error)
    showNotification('Failed to reset token statistics', 'error')
  }
}

// 修改 validateApiKey 中的验证逻辑
const validateApiKey = async (providerId: string) => {
  if (validating[providerId]) {
    return
  }

  validating[providerId] = true

  try {
    const key = apiKeys[providerId]
    if (!key) {
      // 清除该提供商的验证状态和保存的 key
      delete validatedKeys[providerId]
      const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.VALIDATED_KEYS, STORAGE_KEYS.SETTINGS])
      const keys = storage[STORAGE_KEYS.API_KEYS] || {}
      const validated = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
      const settings = storage[STORAGE_KEYS.SETTINGS] || {}

      delete keys[providerId]
      delete validated[providerId]

      // 如果清除的是当前默认提供商，重置为第一个有效的提供商
      if (settings.defaultProvider === providerId) {
        const firstValidProvider = Object.keys(validated).find(key => validated[key])
        if (firstValidProvider) {
          settings.defaultProvider = firstValidProvider
        } else {
          // 如果没有有效的提供商，则设置为空字符串
          settings.defaultProvider = ''
          settings.defaultModel = ''
        }
      }

      // 先保存更改到存储
      await chrome.storage.sync.set({
        [STORAGE_KEYS.API_KEYS]: keys,
        [STORAGE_KEYS.VALIDATED_KEYS]: validated,
        [STORAGE_KEYS.SETTINGS]: settings
      })

      // 然后再更新模型选项，确保使用最新的存储数据
      if (settings.defaultProvider !== providerId) {
        await updateModelOptions(false)
      }

      validating[providerId] = false
      return
    }

    // 直接验证 API Key
    let endpoint = '';
    let testPayload = {};

    switch (providerId) {
      case 'openai':
        endpoint = 'https://api.openai.com/v1/chat/completions';
        testPayload = {
          model: "gpt-3.5-turbo",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;

      case 'claude':
        endpoint = 'https://api.anthropic.com/v1/models';
        break;

      case 'moonshot':
        endpoint = 'https://api.moonshot.cn/v1/chat/completions';
        testPayload = {
          model: "moonshot-v1-8k",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;

      case 'gemini':
        endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
        testPayload = {
          contents: [{ parts: [{ text: "test" }] }]
        };
        break;

      case 'deepseek':
        endpoint = 'https://api.deepseek.com/v1/chat/completions';
        testPayload = {
          model: "deepseek-chat",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;

      case 'openrouter':
        endpoint = 'https://openrouter.ai/api/v1/chat/completions';
        testPayload = {
          model: "openai/gpt-3.5-turbo",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;

      case 'ollama':
        // 对于 Ollama，我们测试连接到本地服务器
        const ollamaEndpoint = key || 'http://localhost:11434'; // key 作为端点地址
        endpoint = `${ollamaEndpoint}/v1/models`;
        // 对于 Ollama，我们只需要测试模型列表端点
        const modelsResponse = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ollama'
          }
        });

        if (!modelsResponse.ok) {
          throw new Error('Cannot connect to Ollama server');
        }

        // Ollama 连接测试成功
        validatedKeys[providerId] = true;

        // 保存验证成功的端点
        const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS])
        const keys = storage[STORAGE_KEYS.API_KEYS] || {}
        const settings = storage[STORAGE_KEYS.SETTINGS] || {}

        keys[providerId] = key;
        settings.ollama_endpoint = key; // 保存端点地址

        // 如果是第一个验证成功的 key，或者当前默认提供商未验证，设置为默认提供商
        const validProviders = Object.keys(validatedKeys).filter(key => validatedKeys[key]);
        if (validProviders.length === 1 || !validatedKeys[settings.defaultProvider]) {
          settings.defaultProvider = providerId;
          // 强制更新UI
          nextTick(() => {
            updateModelOptions(false);
          });
        }

        await chrome.storage.sync.set({
          [STORAGE_KEYS.API_KEYS]: keys,
          [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
          [STORAGE_KEYS.SETTINGS]: settings
        });

        showNotification(`${providers.value.find(p => p.id === providerId)?.name || providerId} connection validated successfully`, 'success');
        validating[providerId] = false;
        return;

      default:
        throw new Error('Unsupported provider');
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // 根据不同提供商设置认证头
    switch (providerId) {
      case 'openai':
        headers['Authorization'] = `Bearer ${key}`;
        break;
      case 'claude':
        headers['x-api-key'] = key;
        headers['anthropic-version'] = '2023-06-01';
        break;
      case 'moonshot':
        headers['Authorization'] = `Bearer ${key}`;
        break;
      case 'gemini':
        endpoint = `${endpoint}?key=${key}`;
        break;
      case 'deepseek':
        headers['Authorization'] = `Bearer ${key}`;
        break;
      case 'openrouter':
        headers['Authorization'] = `Bearer ${key}`;
        headers['HTTP-Referer'] = 'https://fillify.tech';
        headers['X-Title'] = 'Fillify';
        break;
    }

    const response = await fetch(endpoint, {
      method: providerId === 'claude' ? 'GET' : 'POST',
      headers,
      ...(providerId !== 'claude' && { body: JSON.stringify(testPayload) })
    });

    const data = await response.json();

    // 检查响应是否包含错误
    if (response.status !== 200) {
      throw new Error(data.error?.message || 'Invalid API key');
    }

    // API Key 验证成功
    validatedKeys[providerId] = true;

    // 保存验证成功的 key
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS])
    const keys = storage[STORAGE_KEYS.API_KEYS] || {}
    const settings = storage[STORAGE_KEYS.SETTINGS] || {}

    keys[providerId] = key;

    // 如果是第一个验证成功的 key，或者当前默认提供商未验证，设置为默认提供商
    const validProviders = Object.keys(validatedKeys).filter(key => validatedKeys[key]);
    if (validProviders.length === 1 || !validatedKeys[settings.defaultProvider]) {
      settings.defaultProvider = providerId;
      // 强制更新UI
      nextTick(() => {
        updateModelOptions(false);
      });
    }

    await chrome.storage.sync.set({
      [STORAGE_KEYS.API_KEYS]: keys,
      [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
      [STORAGE_KEYS.SETTINGS]: settings
    });

    showNotification(`${providers.value.find(p => p.id === providerId)?.name || providerId} API key validated successfully`, 'success');
  } catch (err) {
    validatedKeys[providerId] = false;
    const errorMessage = err instanceof Error ? err.message : String(err);
    showNotification(`${providers.value.find(p => p.id === providerId)?.name || providerId}: ${errorMessage}`, 'error');
  } finally {
    validating[providerId] = false;
  }
}

// 修改防抖时间，使响应更快但不会太频繁
const debouncedValidateApiKey = debounce(validateApiKey, 800)

// 处理 API Key 输入的函数
// 当输入为空时，直接清除该提供商的 API Key
// 当输入不为空时，调用防抖验证函数
const handleApiKeyInput = async (providerId: string) => {
  const key = apiKeys[providerId]

  // 如果输入为空，直接清除该提供商的 API Key
  if (!key || key.trim() === '') {
    // 如果当前默认提供商是要删除的提供商，先切换默认提供商
    if (settings.defaultProvider === providerId) {
      // 获取存储中的数据
      const storage = await chrome.storage.sync.get([STORAGE_KEYS.VALIDATED_KEYS, STORAGE_KEYS.SETTINGS])
      const validated = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
      const storedSettings = storage[STORAGE_KEYS.SETTINGS] || {}

      // 找出第一个有效的提供商（不包括要删除的提供商）
      const validProviders = Object.keys(validated).filter(key => validated[key] && key !== providerId)

      if (validProviders.length > 0) {
        storedSettings.defaultProvider = validProviders[0]
      } else {
        storedSettings.defaultProvider = ''
        storedSettings.defaultModel = ''
      }

      // 更新本地设置
      settings.defaultProvider = storedSettings.defaultProvider
      settings.defaultModel = storedSettings.defaultModel

      // 保存设置
      await chrome.storage.sync.set({
        [STORAGE_KEYS.SETTINGS]: storedSettings
      })
    }

    // 清除验证状态
    delete validatedKeys[providerId]

    // 保存到存储
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.VALIDATED_KEYS])
    const keys = storage[STORAGE_KEYS.API_KEYS] || {}
    const validated = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}

    delete keys[providerId]
    delete validated[providerId]

    await chrome.storage.sync.set({
      [STORAGE_KEYS.API_KEYS]: keys,
      [STORAGE_KEYS.VALIDATED_KEYS]: validated
    })

    // 更新模型选项
    await updateModelOptions(false)
  } else {
    // 如果输入不为空，调用防抖验证函数
    debouncedValidateApiKey(providerId)
  }
}

const toggleVisibility = (providerId: string) => {
  showPassword[providerId] = !showPassword[providerId]
  const input = document.getElementById(`${providerId}-key`) as HTMLInputElement
  if (input) {
    input.type = showPassword[providerId] ? 'text' : 'password'
  }
}

// 刷新所有 API Key 验证
const refreshApiKeys = async () => {
  try {
    // 显示加载状态
    isLoading.value = true

    // 获取所有已保存的 API Key
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS])
    const keys = storage[STORAGE_KEYS.API_KEYS] || {}

    // 重置验证状态
    Object.keys(validatedKeys).forEach(key => {
      validatedKeys[key] = false
    })

    // 对每个有值的 API Key 进行验证
    const validationPromises = Object.entries(keys).map(async ([providerId, key]) => {
      if (key) {
        apiKeys[providerId] = key as string
        await validateApiKey(providerId)
      }
    })

    // 等待所有验证完成
    await Promise.all(validationPromises)

    // 更新模型选项
    await updateModelOptions(false)

    showNotification('API Keys refreshed successfully', 'success')
  } catch (error) {
    console.error('Error refreshing API keys:', error)
    showNotification('Failed to refresh API keys', 'error')
  } finally {
    isLoading.value = false
  }
}

const updateModelOptions = async (showNotificationMessage = true) => {
  const provider = settings.defaultProvider
  // 获取已验证的API keys
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.VALIDATED_KEYS, STORAGE_KEYS.API_KEYS])
    const storedValidatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
    const storedApiKeys = storage[STORAGE_KEYS.API_KEYS] || {}

    // 更新本地的validatedKeys对象，确保与存储同步
    // 先清除所有不在存储中的键
    Object.keys(validatedKeys).forEach(key => {
      if (!storedValidatedKeys[key]) {
        delete validatedKeys[key]
      }
    })

    // 然后添加或更新存储中的键
    Object.keys(storedValidatedKeys).forEach(key => {
      validatedKeys[key] = storedValidatedKeys[key]
    })

    // 更新本地的apiKeys对象
    // 先清除所有不在存储中的键
    Object.keys(apiKeys).forEach(key => {
      if (!storedApiKeys[key]) {
        delete apiKeys[key]
      }
    })

    // 然后添加或更新存储中的键
    Object.keys(storedApiKeys).forEach(key => {
      apiKeys[key] = storedApiKeys[key]
    })

    // 找出第一个有效的提供商
    const validProviders = Object.keys(validatedKeys).filter(key => validatedKeys[key])

    // 如果当前选中的提供商未验证，但有其他验证过的提供商，则选择第一个有效的
    if (!validatedKeys[provider] && validProviders.length > 0) {
      settings.defaultProvider = validProviders[0]
    }

    // 更新可用模型
    const currentProvider = settings.defaultProvider

    // 如果没有选择提供商，清空模型列表
    if (!currentProvider) {
      availableModels.value = [];
      settings.defaultModel = '';
    }
    // 如果选择了已验证的提供商
    else if (validatedKeys[currentProvider]) {
      try {
        isLoadingModels.value = true;
        let models: Model[] = [];

        // 根据不同的提供商调用相应的获取模型函数
        switch (currentProvider) {
          case 'openai':
            models = await fetchOpenAIModels(apiKeys.openai);
            break;
          case 'claude':
            models = await fetchClaudeModels(apiKeys.claude);
            break;
          case 'moonshot':
            models = await fetchMoonshotModels(apiKeys.moonshot);
            break;
          case 'gemini':
            models = await fetchGeminiModels(apiKeys.gemini);
            break;
          case 'deepseek':
            models = await fetchDeepSeekModels(apiKeys.deepseek);
            break;
          case 'openrouter':
            models = await fetchOpenRouterModels(apiKeys.openrouter);
            break;
          case 'ollama':
            // 获取 Ollama 端点设置
            const storage = await chrome.storage.sync.get([STORAGE_KEYS.SETTINGS]);
            const storedSettings = storage[STORAGE_KEYS.SETTINGS] || {};
            const ollamaEndpoint = storedSettings.ollama_endpoint || apiKeys.ollama || 'http://localhost:11434';
            models = await fetchOllamaModels(ollamaEndpoint);
            break;
          default:
            // 如果没有匹配的提供商，使用静态模型列表
            models = PROVIDER_MODELS[currentProvider as keyof typeof PROVIDER_MODELS] || [];
        }

        availableModels.value = models;

        // 如果当前选中的模型不在新的模型列表中，选择第一个模型
        if (!availableModels.value.find(m => m.id === settings.defaultModel)) {
          settings.defaultModel = availableModels.value[0]?.id || '';
        }
      } catch (error) {
        console.error(`Error fetching ${currentProvider} models:`, error);
        // 如果获取失败，使用静态模型列表
        availableModels.value = PROVIDER_MODELS[currentProvider as keyof typeof PROVIDER_MODELS] || [];
        if (!availableModels.value.find(m => m.id === settings.defaultModel)) {
          settings.defaultModel = availableModels.value[0]?.id || '';
        }
      } finally {
        isLoadingModels.value = false;
      }
    }
    // 如果选择了未验证的提供商
    else {
      availableModels.value = [];
      settings.defaultModel = '';
    }

    // 保存设置
    await saveSettings(showNotificationMessage)
  } catch (error) {
    console.error('Error updating model options:', error)
    showNotification('Failed to update model options', 'error')
  }
}

// 修改 saveSettings 函数
const saveSettings = async (showNotificationMessage = true) => {
  try {
    await chrome.storage.sync.set({ [STORAGE_KEYS.SETTINGS]: settings })

    if (showNotificationMessage) {
      showNotification('Settings saved successfully', 'success')
    }
  } catch (err) {
    const message = handleError(err, 'Failed to save settings')
    showNotification(message, 'error')
  }
}

const openProjectModal = () => {
  console.log('openProjectModal called');
  // 添加延迟，确保模态弹窗在标签页切换完成后再打开
  setTimeout(() => {
    console.log('Setting showProjectModal to true');
    showProjectModal.value = true;
    console.log('Current value of showProjectModal:', showProjectModal.value);
  }, 100);
}

const showNotification = (message: string, type: 'success' | 'error') => {
  // 清除之前的定时器
  if (notification.timer) {
    clearTimeout(notification.timer)
  }

  // 更新通知状态
  Object.assign(notification, {
    show: true,
    message,
    type,
    timer: setTimeout(() => {
      hideNotification()
    }, 3000)
  })
}

const hideNotification = () => {
  notification.show = false
  if (notification.timer) {
    clearTimeout(notification.timer)
    notification.timer = undefined
  }
}

// 项目相关方法
const editProject = (project: Project) => {
  editingProject.value = project
  Object.assign(projectForm, project)
  showProjectModal.value = true
}

const closeProjectModal = () => {
  showProjectModal.value = false
  editingProject.value = null
  Object.assign(projectForm, {
    name: '',
    description: '',
    environment: '',
    template: ''
  })
}

const saveProject = async () => {
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.PROJECTS])
    const currentProjects = storage[STORAGE_KEYS.PROJECTS] || []

    if (editingProject.value) {
      // 更新现有项目
      const index = currentProjects.findIndex((p: Project) => p.id === editingProject.value?.id)
      if (index !== -1) {
        currentProjects[index] = {
          ...editingProject.value,
          ...projectForm
        }
      }
    } else {
      // 添加新项目
      currentProjects.push({
        id: typeof crypto.randomUUID === 'function' ? crypto.randomUUID() : Date.now().toString(36) + Math.random().toString(36).substr(2),
        ...projectForm
      })
    }

    await chrome.storage.sync.set({ [STORAGE_KEYS.PROJECTS]: currentProjects })
    projects.value = currentProjects
    showNotification(
      `Project ${editingProject.value ? 'updated' : 'added'} successfully`,
      'success'
    )
    closeProjectModal()
  } catch (error) {
    showNotification(
      `Failed to ${editingProject.value ? 'update' : 'add'} project`,
      'error'
    )
  }
}
const deleteProject = async (projectId: string) => {
  if (!confirm('Are you sure you want to delete this project?')) {
    return
  }

  try {
    const updatedProjects = projects.value.filter(p => p.id !== projectId)
    await chrome.storage.sync.set({ [STORAGE_KEYS.PROJECTS]: updatedProjects })
    projects.value = updatedProjects
    showNotification('Project deleted successfully', 'success')
  } catch (error) {
    showNotification('Failed to delete project', 'error')
  }
}

// 格式化日期
const formatDate = (isoString: string): string => {
  try {
    if (!isoString) return "N/A"

    const date = new Date(isoString)
    if (isNaN(date.getTime())) return "Invalid date"

    // 格式化为 YYYY-MM-DD HH:MM
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false
    }).format(date)
  } catch (error) {
    // Error formatting date
    return "Error"
  }
}

// 修改 debouncedSaveSettings 中的错误处理
const debouncedSaveSettings = debounce(async (newSettings: Settings) => {
  try {
    await chrome.storage.sync.set({ [STORAGE_KEYS.SETTINGS]: newSettings })
  } catch (err) {
    const message = handleError(err, '保存设置失败')
    showNotification(message, 'error')
  }
}, 500)

// 监听设置变化
watch(settings, (newSettings) => {
  debouncedSaveSettings(newSettings)
}, { deep: true })

// 监听模态弹窗状态变化
watch(showProjectModal, () => {
  // Modal visibility changed
})

// 修改消息监听器处理逻辑
chrome.runtime.onMessage.addListener(async (message) => {
  if (message.type === 'apiKeyValidationResult') {
    const { provider, result } = message

    if (result.success) {
      // API Key 验证成功
      validatedKeys[provider] = true

      // 保存验证成功的 key
      const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS])
      const keys = storage[STORAGE_KEYS.API_KEYS] || {}
      const settings = storage[STORAGE_KEYS.SETTINGS] || {}

      keys[provider] = apiKeys[provider]

      // 如果是第一个验证成功的 key，或者当前默认提供商未验证，设置为默认提供商
      const validProviders = Object.keys(validatedKeys).filter(key => validatedKeys[key])
      if (validProviders.length === 1 || !validatedKeys[settings.defaultProvider]) {
        settings.defaultProvider = provider
        // 强制更新UI
        nextTick(() => {
          updateModelOptions(false)
        })
      }

      await chrome.storage.sync.set({
        [STORAGE_KEYS.API_KEYS]: keys,
        [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
        [STORAGE_KEYS.SETTINGS]: settings
      })

      showNotification(`${providers.value.find(p => p.id === provider)?.name || provider} API key validated successfully`, 'success')
    } else {
      // API Key 验证失败
      validatedKeys[provider] = false
      showNotification(result.error || `${providers.value.find(p => p.id === provider)?.name || provider} API key is invalid`, 'error')
    }
    validating[provider] = false
  }
})

// 初始化
onMounted(async () => {
  // 将打开模态弹窗的方法挂载到全局对象上，使 settings.js 可以调用
  (window as any).openProjectModalVue = () => {
    showProjectModal.value = true
  }

  // 直接检查 URL 参数
  const urlParams = new URLSearchParams(window.location.search)
  const action = urlParams.get('action')
  const tab = urlParams.get('tab')

  // Check URL parameters

  // 如果有 tab 参数，切换到对应标签页
  if (tab) {
    // Switch to the specified tab
    switchTab(tab)
    // 直接设置 activeTab 值，确保 UI 更新
    activeTab.value = tab
  }

  // 如果 action 是 add_project，打开模态弹窗
  if (action === 'add_project') {
    // Open project modal from URL parameter
    // 增加延迟时间，确保在标签页切换完成后再打开模态弹窗
    setTimeout(() => {
      showProjectModal.value = true
    }, 500)
  }

  isLoading.value = true
  try {
    // 获取用户功能标志（需要登录态）
    isLoadingFeatures.value = true
    try {
      const featureResponse = await fetchUserFeatureFlags()
      if (featureResponse.success && featureResponse.features) {
        Object.assign(userFeatures, featureResponse.features)
      }
    } catch (error) {
      console.warn('Failed to fetch user feature flags:', error)
      // 失败时使用默认值，不显示错误通知
    } finally {
      isLoadingFeatures.value = false
    }

    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.VALIDATED_KEYS,
      STORAGE_KEYS.TOKEN_STATS,
      STORAGE_KEYS.PROJECTS,
      STORAGE_KEYS.ACTIVE_TAB
    ])

    // 初始化 API keys
    Object.assign(apiKeys, storage[STORAGE_KEYS.API_KEYS] || {})
    Object.assign(validatedKeys, storage[STORAGE_KEYS.VALIDATED_KEYS] || {})

    // 为Ollama设置默认地址（如果没有设置过）
    if (!apiKeys.ollama) {
      apiKeys.ollama = OLLAMA_DEFAULT_ENDPOINT
    }

    // 初始化设置
    const storedSettings = storage[STORAGE_KEYS.SETTINGS] || {}
    // 确保useCustomApi有默认值
    if (storedSettings.useCustomApi === undefined) {
      storedSettings.useCustomApi = true // 默认使用Custom API
    }
    Object.assign(settings, storedSettings)

    // 检查是否有验证过的API key
    const validProviders = Object.keys(validatedKeys).filter(key => validatedKeys[key])

    // 如果没有验证过的API key，则将defaultProvider设置为空字符串
    if (validProviders.length === 0) {
      settings.defaultProvider = ''
      settings.defaultModel = ''
    }
    // 如果有验证过的API key，但当前选中的提供商未验证，则选择第一个验证过的
    else if (validProviders.length > 0 && !validatedKeys[settings.defaultProvider]) {
      settings.defaultProvider = validProviders[0]
    }

    // 初始化 token 统计
    const rawTokenStats = storage[STORAGE_KEYS.TOKEN_STATS] || {}
    // Process token stats from storage

    // 检查token统计数据结构
    if (Object.keys(rawTokenStats).length > 0) {
      // 转换token统计数据格式
      for (const provider in rawTokenStats) {
        const providerData = rawTokenStats[provider]

        // 检查数据结构是否符合新的格式要求
        if (typeof providerData === 'object' && providerData !== null) {
          if ('promptTokens' in providerData && 'completionTokens' in providerData) {
            // 已经是新格式，直接使用
            tokenStats[provider] = providerData
          } else if ('requests' in providerData && 'tokens' in providerData) {
            // 旧格式数据，需要转换为新格式
            tokenStats[provider] = {
              promptTokens: Math.floor(providerData.tokens * 0.7), // 估算：约70%是prompt tokens
              completionTokens: Math.floor(providerData.tokens * 0.3), // 估算：约30%是completion tokens
              totalTokens: providerData.tokens,
              lastUpdated: new Date().toISOString()
            }
          } else {
            // 未知格式，创建默认结构
            // Unknown token stats format detected
            tokenStats[provider] = {
              promptTokens: 0,
              completionTokens: 0,
              totalTokens: 0,
              lastUpdated: new Date().toISOString()
            }
          }
        } else {
          // 非对象类型，创建默认结构
          // Invalid token stats detected
          tokenStats[provider] = {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
            lastUpdated: new Date().toISOString()
          }
        }
      }
    }

    // Token stats processed

    // 初始化项目列表
    projects.value = storage[STORAGE_KEYS.PROJECTS] || []

    // 初始化活动标签页
    const savedTab = storage[STORAGE_KEYS.ACTIVE_TAB]
    if (savedTab) {
      activeTab.value = savedTab
    }

    // 初始化可用模型，不显示通知
    updateModelOptions(false)
    
    // 检查Ollama连接状态
    if (apiKeys.ollama) {
      checkOllamaConnection(apiKeys.ollama)
    }
  } catch (error) {
    showNotification('Failed to initialize settings', 'error')
  } finally {
    isLoading.value = false
  }
})

// 在组件卸载时清理
onUnmounted(() => {
  if (notification.timer) {
    clearTimeout(notification.timer)
  }
})
</script>

<style>
/* CSS 已经在 settings.css 中定义 */
</style>

<style scoped>
/* 修改 loading spinner 样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Full-page loading spinner */
.page-spinner {
  width: 56px;
  height: 56px;
  border: 4px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin360 0.9s linear infinite;
  box-shadow: 0 0 0 1px rgba(0,0,0,0.03) inset;
}

@keyframes spin360 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Providers Grid 样式 */
.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* Provider Card 样式 */
.provider-card {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  background-color: var(--bg-secondary);
}

.provider-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-color: var(--primary-color);
}

.provider-header {
  margin-bottom: 12px;
}

.provider-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary);
  font-weight: 500;
}

/* API Key 帮助链接样式 */
.api-key-help {
  margin-top: 8px;
  text-align: right;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.provider-card:hover .api-key-help {
  opacity: 1;
}

.api-key-help a {
  color: #1D5DF4;
  text-decoration: none;
  transition: color 0.2s;
}

.api-key-help a:hover {
  color: #1850D8;
  text-decoration: underline;
}

.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.select-wrapper .loading-spinner {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  pointer-events: none;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* API Key 输入框中的小型 loading spinner */
.key-input-group .loading-spinner {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%) rotate(0deg);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.key-input-group .loading-spinner.active {
  opacity: 1;
  border-color: var(--border-color);
  border-top-color: var(--primary-color);
}

@keyframes spin {
  from {
    transform: translateY(-50%) rotate(0deg);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* 通知组件样式 */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 8px;
  background: var(--bg-primary);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  cursor: pointer;
  animation: slideIn 0.3s ease-out;
  transition: transform 0.2s ease-out;
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification::before {
  content: '';
  width: 20px;
  height: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.success::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2316a34a'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.error::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23dc2626'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

.notification:hover {
  transform: translateY(-3px);
}

/* API Key 输入框样式 */
.key-input-group {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  min-height: 38px;
}

.key-input {
  flex: 1;
  padding-right: 70px;
  transition: all 0.3s ease;
}

.key-input.validating {
  border-color: var(--warning-color);
  background-color: rgba(217, 119, 6, 0.05);
  box-shadow: 0 0 0 1px var(--warning-color);
}

.key-input.valid {
  border-color: var(--success-color);
  background-color: rgba(22, 163, 74, 0.05);
  box-shadow: 0 0 0 1px var(--success-color);
}

.key-input.invalid {
  border-color: var(--danger-color);
  background-color: rgba(220, 38, 38, 0.05);
  box-shadow: 0 0 0 1px var(--danger-color);
}



.visibility-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
  z-index: 3;
}

.visibility-toggle:hover {
  opacity: 1;
}

.eye-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
  transition: transform 0.2s ease;
}

.visibility-toggle:hover .eye-icon {
  transform: scale(1.1);
}

/* 加载动画样式 */
.loading-spinner.active {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}

.no-data-message {
  text-align: center;
  color: var(--text-secondary);
  margin-top: 1rem;
}

/* 模态框相关样式在 modal.css 中定义 */

/* 刷新按钮样式 */
.provider-refresh-button {
  background-color: var(--primary-color-light);
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.8;
}

.provider-refresh-button:hover {
  background-color: var(--primary-color-light);
  opacity: 1;
  transform: scale(1.1);
}

.provider-refresh-button:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}

.refresh-icon {
  width: 18px;
  height: 18px;
  fill: var(--primary-color);
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.2));
}

.provider-refresh-button:hover .refresh-icon {
  transform: rotate(180deg);
}

.refresh-icon.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>

<style scoped>
.last-updated {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px dashed var(--border-color);
  font-size: 0.9em;
  color: var(--text-secondary);
}

.last-updated .stat-value {
  font-style: italic;
}

/* Ollama状态指示器样式 */
.ollama-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-dot.online {
  background-color: #22c55e;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.status-dot.offline {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.checking {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-weight: 500;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.provider-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary);
  font-weight: 500;
}
</style>

import { createApp } from 'vue'
import App from './App.vue'
import './settings.css'
import './modal.css'
import './button-styles.css'

// Disable all console output for production
const __disableConsole = () => {
  const noop = (..._args: any[]) => {};
  const methods: (keyof Console)[] = ['log', 'info', 'warn', 'error', 'debug', 'trace'];
  for (const m of methods) {
    try { (console as any)[m] = noop } catch {}
  }
};
__disableConsole();

const app = createApp(App)

// 创建一个全局属性，用于存储 URL 参数
const urlParams = new URLSearchParams(window.location.search)
const action = urlParams.get('action')
const tab = urlParams.get('tab')

app.config.globalProperties.$urlParams = {
  action,
  tab
}

// 挂载应用
const appInstance = app.mount('#app') as any

// 将应用实例暴露给 window 对象，以便在控制台中调试
(window as any).__VUE_APP_INSTANCE__ = appInstance
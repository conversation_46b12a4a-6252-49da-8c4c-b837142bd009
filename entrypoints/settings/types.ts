export interface StorageKeys {
  API_KEYS: string
  SETTINGS: string
  PROJECTS: string
  TOKEN_STATS: string
  CUSTOM_PROVIDERS: string
  FIRST_VISIT: string
  COLLAPSED_SECTIONS: string
  ACTIVE_TAB: string
  VALIDATED_KEYS: string
}

export interface TokenStats {
  promptTokens: number
  completionTokens: number
  totalTokens: number
  lastUpdated: string
}

export interface Provider {
  id: string
  name: string
}

export interface Model {
  id: string
  name: string
  isFree?: boolean
}

export interface Settings {
  useCustomApi: boolean
  defaultProvider: string
  defaultModel: string
  showProjectDetails: boolean
  showReasoningContent: boolean
}

export interface Project {
  id: string
  name: string
  description: string
  environment: string
  template: string
}

export interface ProjectForm {
  name: string
  description: string
  environment: string
  template: string
}

export interface Notification {
  show: boolean
  message: string
  type: 'success' | 'error'
  timer?: number
}

export interface UserFeatureFlags {
  showCustomApiToggle: boolean
}

export interface UserFeatureResponse {
  success: boolean
  features?: UserFeatureFlags
  error?: string
}
import type { UserFeatureResponse } from './types'

export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: number | undefined

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

export const handleError = (error: unknown, context: string) => {
  console.error(`[${context}]`, error)
  const errorMessage = error instanceof Error ? error.message : String(error)
  return `${context}: ${errorMessage}`
}

/**
 * 获取当前登录状态和用户信息
 * 通过background script获取最新的登录状态，确保token的有效性
 */
const getCurrentAuthInfo = async (): Promise<{ isLoggedIn: boolean; userInfo: any; token: string } | null> => {
  try {
    // 1. 首先通过background获取登录状态
    const loginStatus = await chrome.runtime.sendMessage({ type: 'getLoginStatus' })
    if (!loginStatus?.isLoggedIn) {
      return null
    }

    // 2. 获取用户信息（包含token）
    const userInfoResponse = await chrome.runtime.sendMessage({ type: 'getUserInfo' })
    if (!userInfoResponse?.success || !userInfoResponse?.user) {
      return null
    }

    // 3. 通过background获取最新的cookie token作为备用
    const cookieToken = await new Promise<string | null>((resolve) => {
      chrome.runtime.sendMessage({ type: 'getCurrentToken' }, (response) => {
        resolve(response?.token || null)
      })
    }).catch(() => null)

    // 优先使用用户信息中的token，如果没有则使用cookie token
    const token = userInfoResponse.user.token || cookieToken
    
    if (!token) {
      console.warn('[Settings] No valid token found')
      return null
    }

    return {
      isLoggedIn: true,
      userInfo: userInfoResponse.user,
      token
    }
  } catch (error) {
    console.error('[Settings] Error getting auth info:', error)
    return null
  }
}

/**
 * 获取用户功能标志
 * 需要用户登录态才能调用
 * 优化后的版本：通过background script获取最新的登录状态和token
 */
export const fetchUserFeatureFlags = async (): Promise<UserFeatureResponse> => {
  try {
    // 通过优化的认证机制获取登录信息
    const authInfo = await getCurrentAuthInfo()
    if (!authInfo) {
      return {
        success: false,
        error: 'User not logged in or no valid token available'
      }
    }

    // 向后端请求用户功能标志
    const response = await fetch('https://fillify-343190162770.asia-east1.run.app/api/users/features', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authInfo.token}`,
        'X-User-ID': authInfo.userInfo.id
      }
    })

    if (!response.ok) {
      // 提供更详细的错误信息
      const errorText = await response.text().catch(() => 'Unknown error')
      throw new Error(`HTTP ${response.status}: ${response.statusText}. ${errorText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      features: {
        showCustomApiToggle: data.features?.showCustomApiToggle || false
      }
    }
  } catch (error) {
    console.error('Failed to fetch user feature flags:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      // 默认情况下不显示Custom API选项
      features: {
        showCustomApiToggle: false
      }
    }
  }
} 
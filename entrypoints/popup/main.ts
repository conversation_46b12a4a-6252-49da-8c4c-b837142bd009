import { createApp } from 'vue';
import App from './App.vue';

// Disable all console output for production
const __disableConsole = () => {
  const noop = (..._args: any[]) => {};
  const methods: (keyof Console)[] = ['log', 'info', 'warn', 'error', 'debug', 'trace'];
  for (const m of methods) {
    try { (console as any)[m] = noop } catch {}
  }
};
__disableConsole();

createApp(App).mount('#app');

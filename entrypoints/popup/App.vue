<script lang="ts" setup>
/// <reference types="chrome"/>

import { ref, onMounted } from 'vue'

// 状态管理
const errorTitle = ref('Page Analysis Issue')
const errorMessage = ref('This page cannot be analyzed by Fillify.')
const showRetryButton = ref(false)
const errorReason = ref('')

// 更新错误信息的函数
const updateErrorInfo = (reason: string, pageStatus?: any) => {
  errorReason.value = reason

  // 统一使用您设计的文案
  errorTitle.value = "Oops! Nothing to fill in on this page 🙈"
  errorMessage.value = "This might be a blank page, an option page, or there may simply be no form fields available. Try refreshing the page or visiting a different site."

  // 根据不同情况决定是否显示重试按钮
  switch (reason) {
    case 'no_form_fields':
    case 'no_active_tab':
    case 'special_page':
      showRetryButton.value = false
      break
    default:
      showRetryButton.value = true
  }
}

// 事件处理函数
const handleRefresh = async () => {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      return
    }

    // 刷新当前网页
    chrome.tabs.reload(tab.id).then(() => {
      console.log('Page refreshed successfully')
      // 关闭popup
      window.close()
    }).catch(error => {
      console.error('Failed to refresh page:', error)
      // 即使刷新失败也关闭popup
      window.close()
    })
  } catch (error) {
    console.error('Error during refresh:', error)
    window.close()
  }
}

const handleClose = () => {
  window.close()
}

// 初始化函数
onMounted(async () => {
  try {
    // 检查是否是由于注入popup错误触发的
    const errorTrigger = await chrome.storage.local.get('popup_error_trigger');
    if (errorTrigger.popup_error_trigger) {
      const trigger = errorTrigger.popup_error_trigger;
      // 检查时间戳，确保是最近的错误（5秒内）
      if (Date.now() - trigger.timestamp < 5000) {
        console.log('Popup opened due to in-page popup error:', trigger.reason);
        updateErrorInfo(trigger.reason, trigger.pageStatus);
        
        // 清除错误触发信息
        chrome.storage.local.remove('popup_error_trigger');
        return;
      }
    }

    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      updateErrorInfo('no_active_tab');
      return;
    }

    // 检查页面状态
    const pageStatusResponse = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
      .catch(() => {
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: 'Communication failed'
        };
      });

    // 显示相应的错误信息（因为这个popup只在有问题时才会显示）
    if (!pageStatusResponse?.isValid) {
      updateErrorInfo('page_invalid', pageStatusResponse);
    } else if (pageStatusResponse.needsRefresh) {
      updateErrorInfo('page_needs_refresh', pageStatusResponse);
    } else if (!pageStatusResponse.hasFormFields) {
      updateErrorInfo('no_form_fields', pageStatusResponse);
    } else {
      // 如果页面状态正常但还是显示了popup，说明可能是手动打开的或其他原因
      updateErrorInfo('popup_creation_failed');
    }
  } catch (error) {
    console.error('Error during popup initialization:', error);
    updateErrorInfo('initialization_error');
  }
})
</script>

<template>
  <div class="popup-container">
    <div class="error-content">
      <h3 class="error-title">{{ errorTitle }}</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <button @click="handleRefresh" class="refresh-btn" v-if="showRetryButton">
          Refresh
        </button>
        <button @click="handleClose" class="close-btn">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.popup-container {
  width: 100%;
  min-height: 160px;
  background: linear-gradient(135deg, #f2f6ff 0%, #e8efff 100%);
  border-radius: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.popup-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(41, 98, 255, 0.06) 0%, transparent 55%),
    radial-gradient(circle at 80% 80%, rgba(30, 78, 227, 0.05) 0%, transparent 55%);
  pointer-events: none;
}

.error-content {
  padding: 24px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.error-title {
  margin: 0 0 12px 0;
  font-size: 17px;
  font-weight: 600;
  color: #374151;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.error-message {
  margin: 0 0 20px 0;
  font-size: 15px;
  color: #6B7280;
  line-height: 1.5;
  max-width: 420px;
  margin-left: auto;
  margin-right: auto;
}

.error-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.refresh-btn, .close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.refresh-btn {
  background: #2962FF;
  color: white;
}

.refresh-btn:hover {
  background: #1E4EE3;
  transform: translateY(-1px);
}

.close-btn {
  background: #F3F4F6;
  color: #6B7280;
  border: 1px solid #E5E7EB;
}

.close-btn:hover {
  background: #E5E7EB;
  color: #374151;
  transform: translateY(-1px);
}

.refresh-btn:active, .close-btn:active {
  transform: translateY(0);
}
</style>

<style>
/* Reset default UA margins to remove the visual gap between body and the component */
html, body {
  margin: 0;
  padding: 0;
  width: 420px; /* keep popup body the same width */
}

/* Ensure the Vue mount node does not add unexpected spacing */
#app {
  margin: 0;
}

/* Optional but helpful: predictable sizing */
*, *::before, *::after {
  box-sizing: border-box;
}
</style>

/**
 * 通用流式内容处理器
 * 基于观察者模式，支持增量内容更新和推理内容分离
 */

export interface StreamChunk {
  delta: string;
  reasoning?: string;
  metadata?: any;
  isComplete?: boolean;
}

export interface StreamProcessorOptions {
  onUpdate?: (content: string, reasoning?: string) => void;
  onComplete?: (finalContent: string, reasoning?: string) => void;
  onError?: (error: Error) => void;
  enableReasoning?: boolean;
  reasoningTags?: string[];
}

export class StreamProcessor {
  private buffer: string = '';
  private reasoningBuffer: string = '';
  private isInReasoning: boolean = false;
  private options: StreamProcessorOptions;
  private reasoningPatterns: RegExp[];

  constructor(options: StreamProcessorOptions = {}) {
    this.options = {
      enableReasoning: true,
      reasoningTags: ['think', 'thinking', 'reasoning'],
      ...options
    };

    // 构建推理标签的正则表达式
    this.reasoningPatterns = this.options.reasoningTags!.map(tag => 
      new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs')
    );
  }

  /**
   * 处理流式数据块
   */
  processChunk(chunk: StreamChunk): void {
    try {
      console.log('[StreamProcessor] Processing chunk:', {
        deltaLength: chunk.delta?.length || 0,
        isComplete: chunk.isComplete,
        hasReasoning: !!chunk.reasoning,
        hasDirectReasoning: !!chunk.reasoning && chunk.reasoning === chunk.delta
      });

      if (!chunk.delta && !chunk.isComplete) return;

      if (chunk.delta) {
        // 预处理 Unicode 转义
        const processedDelta = this.preprocessContent(chunk.delta);

        // 检查是否是直接的推理内容（如 Ollama 推理模型）
        if (chunk.reasoning && chunk.reasoning === chunk.delta) {
          // 直接推理内容，添加到推理缓冲区
          console.log('[StreamProcessor] Processing direct reasoning content:', processedDelta.substring(0, 50));
          this.reasoningBuffer += processedDelta;
          this.triggerUpdate();
        } else if (this.options.enableReasoning) {
          // 常规推理处理（查找推理标签）
          this.processWithReasoning(processedDelta);
        } else {
          // 不启用推理处理，直接添加到内容缓冲区
          this.buffer += processedDelta;
          this.triggerUpdate();
        }
      }

      if (chunk.isComplete) {
        console.log('[StreamProcessor] Stream complete, final state:', {
          contentLength: this.buffer.length,
          reasoningLength: this.reasoningBuffer.length
        });
        this.complete();
      }
    } catch (error) {
      console.error('[StreamProcessor] Error processing chunk:', error);
      this.options.onError?.(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理包含推理内容的数据块
   */
  private processWithReasoning(delta: string): void {
    let remainingDelta = delta;
    
    while (remainingDelta.length > 0) {
      if (!this.isInReasoning) {
        // 查找推理标签开始
        const reasoningStart = this.findReasoningStart(remainingDelta);

        if (reasoningStart !== null) {
          // 添加推理标签前的内容到主缓冲区
          const beforeReasoning = remainingDelta.substring(0, reasoningStart.index);
          if (beforeReasoning) {
            this.buffer += beforeReasoning;
          }

          // 进入推理模式
          this.isInReasoning = true;
          remainingDelta = remainingDelta.substring(reasoningStart.index + reasoningStart.tagLength);
        } else {
          // 没有推理标签，添加到主缓冲区
          this.buffer += remainingDelta;
          remainingDelta = '';
        }
      } else {
        // 在推理模式中，查找推理标签结束
        const reasoningEnd = this.findReasoningEnd(remainingDelta);

        if (reasoningEnd !== null) {
          // 添加推理内容
          const reasoningContent = remainingDelta.substring(0, reasoningEnd.index);
          if (reasoningContent) {
            this.reasoningBuffer += reasoningContent;
          }

          // 退出推理模式
          this.isInReasoning = false;
          remainingDelta = remainingDelta.substring(reasoningEnd.index + reasoningEnd.tagLength);
        } else {
          // 还在推理中，添加到推理缓冲区
          this.reasoningBuffer += remainingDelta;
          remainingDelta = '';
        }
      }
    }
    
    this.triggerUpdate();
  }

  /**
   * 查找推理标签开始
   */
  private findReasoningStart(content: string): { index: number; tagLength: number } | null {
    const patterns = [
      { pattern: '<think>', length: 7 },
      { pattern: '<thinking>', length: 10 },
      { pattern: '<reasoning>', length: 11 },
      { pattern: '\\u003cthink\\u003e', length: 18 },
      { pattern: '\\u003cthinking\\u003e', length: 21 }
    ];

    for (const { pattern, length } of patterns) {
      const index = content.indexOf(pattern);
      if (index !== -1) {
        return { index, tagLength: length };
      }
    }

    return null;
  }

  /**
   * 查找推理标签结束
   */
  private findReasoningEnd(content: string): { index: number; tagLength: number } | null {
    const patterns = [
      { pattern: '</think>', length: 8 },
      { pattern: '</thinking>', length: 11 },
      { pattern: '</reasoning>', length: 12 },
      { pattern: '\\u003c/think\\u003e', length: 19 },
      { pattern: '\\u003c/thinking\\u003e', length: 22 }
    ];

    for (const { pattern, length } of patterns) {
      const index = content.indexOf(pattern);
      if (index !== -1) {
        return { index, tagLength: length };
      }
    }

    return null;
  }

  /**
   * 预处理内容，处理 Unicode 转义
   */
  private preprocessContent(content: string): string {
    return content
      .replace(/\\u003c/g, '<')
      .replace(/\\u003e/g, '>')
      .replace(/\\u0022/g, '"')
      .replace(/\\u0027/g, "'")
      .replace(/\\u002f/g, '/')
      .replace(/\\u005c/g, '\\');
  }

  /**
   * 触发更新回调
   */
  private triggerUpdate(): void {
    this.options.onUpdate?.(this.buffer, this.reasoningBuffer);
  }

  /**
   * 完成处理
   */
  private complete(): void {
    this.options.onComplete?.(this.buffer, this.reasoningBuffer);
  }

  /**
   * 重置处理器状态
   */
  reset(): void {
    this.buffer = '';
    this.reasoningBuffer = '';
    this.isInReasoning = false;
  }

  /**
   * 获取当前状态
   */
  getState(): { content: string; reasoning: string; isInReasoning: boolean } {
    return {
      content: this.buffer,
      reasoning: this.reasoningBuffer,
      isInReasoning: this.isInReasoning
    };
  }
}

import { z } from 'zod';
import { AIModelError } from './interfaces';

// 推理模型类型定义
export enum ReasoningModelType {
  OPENAI_O1 = 'openai-o1',
  DEEPSEEK_REASONER = 'deepseek-reasoner',
  QWEN_REASONER = 'qwen-reasoner',
  CLAUDE_THINKING = 'claude-thinking',
  OLLAMA_REASONING = 'ollama-reasoning',
  GENERIC_XML = 'generic-xml',
  GENERIC_MARKDOWN = 'generic-markdown',
  CUSTOM = 'custom',
}

// 推理解析配置
export interface ReasoningParseConfig {
  modelType: ReasoningModelType;
  tagPatterns: string[];
  fallbackPatterns?: string[];
  separator: string;
  minReasoningLength: number;
  maxReasoningLength?: number;
  cleanupRules: CleanupRule[];
}

// 清理规则接口
export interface CleanupRule {
  pattern: RegExp;
  replacement: string;
  description: string;
}

// 解析结果
export interface EnhancedReasoningResult {
  reasoning?: string;
  text: string;
  metadata: {
    modelType: ReasoningModelType;
    parseMethod: string;
    reasoningTokens?: number;
    textTokens?: number;
    confidence: number; // 0-1, 解析置信度
    warnings: string[];
  };
}

/**
 * 增强的推理内容解析器
 * 支持多种推理模型格式和自适应解析
 */
export class EnhancedReasoningParser {
  private static readonly MODEL_CONFIGS: Map<ReasoningModelType, ReasoningParseConfig> = new Map([
    [ReasoningModelType.OPENAI_O1, {
      modelType: ReasoningModelType.OPENAI_O1,
      tagPatterns: [
        '<thinking>(.*?)</thinking>',
        // 支持转义格式
        '\\\\u003cthinking\\\\u003e(.*?)\\\\u003c/thinking\\\\u003e',
        '&lt;thinking&gt;(.*?)&lt;/thinking&gt;'
      ],
      fallbackPatterns: ['\\[思考\\](.*?)\\[/思考\\]', '\\[THINKING\\](.*?)\\[/THINKING\\]'],
      separator: '\n\n',
      minReasoningLength: 10,
      maxReasoningLength: 50000,
      cleanupRules: [
        { pattern: /^思考：|^Thinking:|^思路：/gm, replacement: '', description: '移除思考前缀' },
        { pattern: /\n{3,}/g, replacement: '\n\n', description: '规范化换行符' },
      ],
    }],
    
    [ReasoningModelType.DEEPSEEK_REASONER, {
      modelType: ReasoningModelType.DEEPSEEK_REASONER,
      tagPatterns: [
        '<think>(.*?)</think>',
        '<思考>(.*?)</思考>',
        // 支持转义格式
        '\\\\u003cthink\\\\u003e(.*?)\\\\u003c/think\\\\u003e',
        '&lt;think&gt;(.*?)&lt;/think&gt;'
      ],
      fallbackPatterns: ['\\[think\\](.*?)\\[/think\\]', '```thinking(.*?)```'],
      separator: '\n---\n',
      minReasoningLength: 20,
      maxReasoningLength: 100000,
      cleanupRules: [
        { pattern: /^让我思考一下[：:]\s*/gm, replacement: '', description: '移除中文思考引导词' },
        { pattern: /^Let me think[：:]\s*/gm, replacement: '', description: '移除英文思考引导词' },
        { pattern: /^\d+\.\s+/gm, replacement: '', description: '移除数字列表前缀' },
      ],
    }],
    
    [ReasoningModelType.QWEN_REASONER, {
      modelType: ReasoningModelType.QWEN_REASONER,
      tagPatterns: ['<reasoning>(.*?)</reasoning>', '<推理>(.*?)</推理>'],
      fallbackPatterns: ['\\[推理过程\\](.*?)\\[/推理过程\\]', '---推理开始---(.*?)---推理结束---'],
      separator: '\n\n',
      minReasoningLength: 15,
      cleanupRules: [
        { pattern: /^分析：|^Analysis:|^推理：/gm, replacement: '', description: '移除分析前缀' },
        { pattern: /步骤\s*\d+[：:]\s*/g, replacement: '', description: '移除步骤标记' },
      ],
    }],
    
    [ReasoningModelType.CLAUDE_THINKING, {
      modelType: ReasoningModelType.CLAUDE_THINKING,
      tagPatterns: ['<thinking>(.*?)</thinking>', '<analysis>(.*?)</analysis>'],
      fallbackPatterns: ['\\[内部思考\\](.*?)\\[/内部思考\\]', '```thought(.*?)```'],
      separator: '\n\n',
      minReasoningLength: 10,
      cleanupRules: [
        { pattern: /^I need to think about this[：:]\s*/gm, replacement: '', description: '移除英文思考开头' },
        { pattern: /^让我分析一下[：:]\s*/gm, replacement: '', description: '移除中文分析开头' },
      ],
    }],

    [ReasoningModelType.OLLAMA_REASONING, {
      modelType: ReasoningModelType.OLLAMA_REASONING,
      tagPatterns: [
        '<think>(.*?)</think>',
        '<thinking>(.*?)</thinking>',
        '<reasoning>(.*?)</reasoning>',
        // 支持转义格式
        '\\\\u003cthink\\\\u003e(.*?)\\\\u003c/think\\\\u003e',
        '\\\\u003cthinking\\\\u003e(.*?)\\\\u003c/thinking\\\\u003e'
      ],
      fallbackPatterns: [
        '\\[思考\\](.*?)\\[/思考\\]',
        '```reasoning(.*?)```',
        '---推理开始---(.*?)---推理结束---'
      ],
      separator: '\n\n',
      minReasoningLength: 5,
      maxReasoningLength: 200000,
      cleanupRules: [
        { pattern: /^思考：|^Thinking:|^推理：|^Reasoning:/gm, replacement: '', description: '移除推理前缀' },
        { pattern: /\n{3,}/g, replacement: '\n\n', description: '规范化换行符' },
        { pattern: /^\s*[-*]\s*/gm, replacement: '', description: '移除列表标记' },
      ],
    }],
    
    [ReasoningModelType.GENERIC_XML, {
      modelType: ReasoningModelType.GENERIC_XML,
      tagPatterns: [
        '<think>(.*?)</think>',
        '<thinking>(.*?)</thinking>',
        '<reasoning>(.*?)</reasoning>',
        '<thoughts>(.*?)</thoughts>',
        '<analysis>(.*?)</analysis>'
      ],
      separator: '\n\n',
      minReasoningLength: 5,
      cleanupRules: [
        { pattern: /<[^>]*>/g, replacement: '', description: '移除残余XML标签' },
      ],
    }],
    
    [ReasoningModelType.GENERIC_MARKDOWN, {
      modelType: ReasoningModelType.GENERIC_MARKDOWN,
      tagPatterns: [
        '```thinking(.*?)```',
        '```reasoning(.*?)```',
        '```thought(.*?)```'
      ],
      fallbackPatterns: [
        '\\*\\*思考过程[：:]\\*\\*(.*?)\\*\\*结论[：:]\\*\\*',
        '## 思考过程(.*?)## 结论'
      ],
      separator: '\n\n',
      minReasoningLength: 5,
      cleanupRules: [
        { pattern: /```\w*/g, replacement: '', description: '移除代码块标记' },
        { pattern: /#{1,6}\s*/g, replacement: '', description: '移除markdown标题符号' },
      ],
    }],
  ]);

  /**
   * 自动检测推理模型类型
   */
  static detectModelType(modelId: string, content: string): ReasoningModelType {
    const modelIdLower = modelId.toLowerCase();

    // 基于模型ID的检测
    if (modelIdLower.includes('o1') || modelIdLower.includes('openai-o1')) {
      return ReasoningModelType.OPENAI_O1;
    }
    if (modelIdLower.includes('deepseek') && modelIdLower.includes('reason')) {
      return ReasoningModelType.DEEPSEEK_REASONER;
    }
    if (modelIdLower.includes('qwen') && modelIdLower.includes('reason')) {
      return ReasoningModelType.QWEN_REASONER;
    }
    if (modelIdLower.includes('claude') && modelIdLower.includes('thinking')) {
      return ReasoningModelType.CLAUDE_THINKING;
    }
    // 检测 Ollama 推理模型
    if (modelIdLower.includes('gpt-oss') ||
        modelIdLower.includes('qwen') ||
        (modelIdLower.includes('ollama') && (modelIdLower.includes('reason') || modelIdLower.includes('think')))) {
      return ReasoningModelType.OLLAMA_REASONING;
    }

    // 基于内容的检测（支持更多格式）
    const contentLower = content.toLowerCase();

    // 检测 thinking 标签（包括可能的转义格式）
    if (contentLower.includes('<thinking>') || contentLower.includes('</thinking>') ||
        contentLower.includes('\\u003cthinking\\u003e') || content.includes('&lt;thinking&gt;')) {
      if (modelIdLower.includes('openai') || modelIdLower.includes('gpt')) {
        return ReasoningModelType.OPENAI_O1;
      }
      return ReasoningModelType.CLAUDE_THINKING;
    }

    // 检测 think 标签（包括可能的转义格式）
    if (contentLower.includes('<think>') || contentLower.includes('</think>') ||
        contentLower.includes('\\u003cthink\\u003e') || content.includes('&lt;think&gt;')) {
      // 如果模型ID包含 ollama 或 gpt-oss，优先使用 Ollama 推理类型
      if (modelIdLower.includes('ollama') || modelIdLower.includes('gpt-oss')) {
        return ReasoningModelType.OLLAMA_REASONING;
      }
      return ReasoningModelType.DEEPSEEK_REASONER;
    }

    // 检测 reasoning 标签（包括可能的转义格式）
    if (contentLower.includes('<reasoning>') || contentLower.includes('</reasoning>') ||
        contentLower.includes('\\u003creasoning\\u003e') || content.includes('&lt;reasoning&gt;')) {
      // 如果模型ID包含 ollama 或 gpt-oss，优先使用 Ollama 推理类型
      if (modelIdLower.includes('ollama') || modelIdLower.includes('gpt-oss')) {
        return ReasoningModelType.OLLAMA_REASONING;
      }
      return ReasoningModelType.QWEN_REASONER;
    }

    // 检测 markdown 格式
    if (content.includes('```thinking') || content.includes('```reasoning')) {
      return ReasoningModelType.GENERIC_MARKDOWN;
    }

    // 检测通用 XML 格式
    if (/<(analysis|thoughts)>/i.test(content)) {
      return ReasoningModelType.GENERIC_XML;
    }

    return ReasoningModelType.GENERIC_XML; // 默认使用通用 XML 解析
  }

  /**
   * 解析推理内容 - 主入口方法
   */
  static parseReasoningContent(
    content: string,
    modelId: string,
    customConfig?: Partial<ReasoningParseConfig>
  ): EnhancedReasoningResult {
    const modelType = this.detectModelType(modelId, content);
    const config = this.getConfig(modelType, customConfig);
    
    // 尝试主要解析方法
    let result = this.tryParseWithPatterns(content, config.tagPatterns, config);
    
    // 如果主要方法失败，尝试备选方法
    if (!result.reasoning && config.fallbackPatterns) {
      result = this.tryParseWithPatterns(content, config.fallbackPatterns, config);
      result.metadata.parseMethod = 'fallback';
    }
    
    // 如果仍然失败，使用启发式解析
    if (!result.reasoning) {
      result = this.heuristicParse(content, config);
      result.metadata.parseMethod = 'heuristic';
    }

    // 应用清理规则
    if (result.reasoning) {
      result.reasoning = this.applyCleanupRules(result.reasoning, config.cleanupRules);
    }
    result.text = this.applyCleanupRules(result.text, config.cleanupRules);

    // 验证结果质量
    this.validateParseResult(result, config);
    
    return result;
  }

  /**
   * 使用模式匹配解析
   */
  private static tryParseWithPatterns(
    content: string,
    patterns: string[],
    config: ReasoningParseConfig
  ): EnhancedReasoningResult {
    for (const pattern of patterns) {
      try {
        const regex = new RegExp(pattern, 'gs');
        const matches = Array.from(content.matchAll(regex));
        
        if (matches.length > 0) {
          // 提取推理内容
          const reasoningParts = matches.map(match => match[1]?.trim()).filter(Boolean);
          const reasoning = reasoningParts.join(config.separator);
          
          // 移除推理标签，获取最终文本
          const text = content.replace(regex, '').replace(/\n{3,}/g, '\n\n').trim();
          
          return {
            reasoning: reasoning || undefined,
            text: text || content,
            metadata: {
              modelType: config.modelType,
              parseMethod: 'pattern-match',
              reasoningTokens: this.estimateTokens(reasoning),
              textTokens: this.estimateTokens(text),
              confidence: this.calculateConfidence(reasoning, text),
              warnings: [],
            },
          };
        }
      } catch (error) {
        console.warn(`Pattern parsing failed for pattern: ${pattern}`, error);
        continue;
      }
    }

    return {
      text: content,
      metadata: {
        modelType: config.modelType,
        parseMethod: 'none',
        textTokens: this.estimateTokens(content),
        confidence: 0,
        warnings: ['No matching patterns found'],
      },
    };
  }

  /**
   * 启发式解析 - 当模式匹配失败时使用
   */
  private static heuristicParse(
    content: string,
    config: ReasoningParseConfig
  ): EnhancedReasoningResult {
    const warnings: string[] = [];
    
    // 尝试检测思考过程的特征
    const thinkingIndicators = [
      /^(让我|我需要|我应该|需要考虑|分析一下|思考|首先|接下来|然后|最后).{10,}/gm,
      /^(Let me|I need to|I should|Consider|Think about|First|Next|Then|Finally).{10,}/gm,
      /^\d+[\.\)]\s*.{20,}/gm, // 编号列表
      /^[-\*]\s*.{20,}/gm,     // 项目列表
    ];

    const lines = content.split('\n');
    const reasoningLines: string[] = [];
    const textLines: string[] = [];
    let inReasoningMode = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // 检测是否为思考过程行
      const isThinkingLine = thinkingIndicators.some(pattern => pattern.test(line));
      
      if (isThinkingLine && !inReasoningMode) {
        // 开始推理部分
        inReasoningMode = true;
        reasoningLines.push(line);
      } else if (inReasoningMode) {
        // 判断是否继续推理还是转为答案
        if (this.isAnswerLine(line)) {
          inReasoningMode = false;
          textLines.push(line);
        } else {
          reasoningLines.push(line);
        }
      } else {
        textLines.push(line);
      }
    }

    const reasoning = reasoningLines.length > 0 ? reasoningLines.join('\n') : undefined;
    const text = textLines.length > 0 ? textLines.join('\n') : content;

    if (reasoning && reasoning.length < config.minReasoningLength) {
      warnings.push('Extracted reasoning content is too short');
      return {
        text: content,
        metadata: {
          modelType: config.modelType,
          parseMethod: 'heuristic-failed',
          textTokens: this.estimateTokens(content),
          confidence: 0,
          warnings,
        },
      };
    }

    return {
      reasoning,
      text,
      metadata: {
        modelType: config.modelType,
        parseMethod: 'heuristic',
        reasoningTokens: this.estimateTokens(reasoning),
        textTokens: this.estimateTokens(text),
        confidence: reasoning ? 0.6 : 0.2,
        warnings,
      },
    };
  }

  /**
   * 判断是否为答案行
   */
  private static isAnswerLine(line: string): boolean {
    const answerPatterns = [
      /^(所以|因此|综上|总之|答案是|结论是|最终)/,
      /^(So|Therefore|In conclusion|The answer is|Finally)/,
      /^(答:|结论:|Answer:|Conclusion:)/,
    ];
    
    return answerPatterns.some(pattern => pattern.test(line));
  }

  /**
   * 应用清理规则
   */
  private static applyCleanupRules(text: string, rules: CleanupRule[]): string {
    let cleaned = text;
    
    for (const rule of rules) {
      try {
        cleaned = cleaned.replace(rule.pattern, rule.replacement);
      } catch (error) {
        console.warn(`Cleanup rule failed: ${rule.description}`, error);
      }
    }
    
    return cleaned.trim();
  }

  /**
   * 验证解析结果质量
   */
  private static validateParseResult(
    result: EnhancedReasoningResult,
    config: ReasoningParseConfig
  ): void {
    // 检查推理内容长度
    if (result.reasoning) {
      if (result.reasoning.length < config.minReasoningLength) {
        result.metadata.warnings.push('Reasoning content is too short');
        result.metadata.confidence = Math.max(0, result.metadata.confidence - 0.3);
      }
      
      if (config.maxReasoningLength && result.reasoning.length > config.maxReasoningLength) {
        result.metadata.warnings.push('Reasoning content is unusually long');
        result.metadata.confidence = Math.max(0, result.metadata.confidence - 0.1);
      }
    }

    // 检查文本内容质量
    if (!result.text || result.text.trim().length < 5) {
      result.metadata.warnings.push('Final text content is too short');
      result.metadata.confidence = Math.max(0, result.metadata.confidence - 0.4);
    }

    // 检查内容重复性
    if (result.reasoning && result.text && this.calculateSimilarity(result.reasoning, result.text) > 0.8) {
      result.metadata.warnings.push('High similarity between reasoning and text');
      result.metadata.confidence = Math.max(0, result.metadata.confidence - 0.2);
    }
  }

  /**
   * 计算置信度
   */
  private static calculateConfidence(reasoning?: string, text?: string): number {
    let confidence = 0;
    
    if (reasoning && reasoning.length > 50) confidence += 0.4;
    if (text && text.length > 20) confidence += 0.3;
    if (reasoning && text) {
      // 检查内容的互补性
      const similarity = this.calculateSimilarity(reasoning, text);
      confidence += (1 - similarity) * 0.3;
    }
    
    return Math.min(1, confidence);
  }

  /**
   * 计算文本相似度
   */
  private static calculateSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * 简单的 token 估算
   */
  private static estimateTokens(text?: string): number {
    if (!text) return 0;
    return Math.ceil(text.length / 4);
  }

  /**
   * 获取配置
   */
  private static getConfig(
    modelType: ReasoningModelType,
    customConfig?: Partial<ReasoningParseConfig>
  ): ReasoningParseConfig {
    const baseConfig = this.MODEL_CONFIGS.get(modelType) || this.MODEL_CONFIGS.get(ReasoningModelType.GENERIC_XML)!;
    return { ...baseConfig, ...customConfig };
  }

  /**
   * 注册自定义模型配置
   */
  static registerModelConfig(modelType: ReasoningModelType, config: ReasoningParseConfig): void {
    this.MODEL_CONFIGS.set(modelType, config);
  }

  /**
   * 获取支持的模型类型
   */
  static getSupportedModelTypes(): ReasoningModelType[] {
    return Array.from(this.MODEL_CONFIGS.keys());
  }
}

/**
 * 便捷的全局解析函数
 */
export function parseReasoningContent(
  content: string,
  modelId: string,
  customConfig?: Partial<ReasoningParseConfig>
): EnhancedReasoningResult {
  return EnhancedReasoningParser.parseReasoningContent(content, modelId, customConfig);
}
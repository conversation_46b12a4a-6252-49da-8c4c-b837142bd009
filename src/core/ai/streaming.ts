import { StreamChunk, AIModelError } from './interfaces';

// Stream processor interface for handling different streaming formats
export interface StreamProcessor {
  name: string;
  canProcess(response: Response): boolean;
  processStream(response: Response): AsyncIterable<StreamChunk>;
}

// Server-Sent Events (SSE) stream processor
export class SSEStreamProcessor implements StreamProcessor {
  name = 'sse';

  canProcess(response: Response): boolean {
    const contentType = response.headers.get('content-type') || '';
    return contentType.includes('text/event-stream') || 
           contentType.includes('text/plain');
  }

  async *processStream(response: Response): AsyncIterable<StreamChunk> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: 'No readable stream available',
        retryable: false,
      });
    }

    const decoder = new TextDecoder();
    let buffer = '';
    let totalTokens = 0;

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          // Process any remaining buffer
          if (buffer.trim()) {
            const lastChunk = this.parseSSEData(buffer.trim());
            if (lastChunk) {
              yield { ...lastChunk, isComplete: true };
            }
          }
          break;
        }

        // Decode chunk and add to buffer
        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          const chunk = this.parseSSELine(line);
          if (chunk) {
            totalTokens += chunk.delta.length * 0.25; // Rough token estimation
            yield {
              ...chunk,
              usage: chunk.usage || { 
                promptTokens: 0, 
                completionTokens: Math.floor(totalTokens), 
                totalTokens: Math.floor(totalTokens) 
              },
            };
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  private parseSSELine(line: string): StreamChunk | null {
    line = line.trim();
    
    if (!line || line.startsWith(':')) return null; // Comment or empty
    
    if (line === 'data: [DONE]') {
      return { delta: '', isComplete: true };
    }

    if (line.startsWith('data: ')) {
      const data = line.substring(6);
      return this.parseSSEData(data);
    }

    return null;
  }

  private parseSSEData(data: string): StreamChunk | null {
    try {
      const parsed = JSON.parse(data);

      // OpenAI format with reasoning support (for Ollama reasoning models)
      if (parsed.choices?.[0]?.delta) {
        const delta = parsed.choices[0].delta;
        const content = delta.content || '';
        const reasoning = delta.reasoning || '';

        // 优先处理推理内容
        if (reasoning) {
          return {
            delta: reasoning,
            isComplete: false,
            metadata: {
              isReasoning: true,
              originalDelta: delta
            }
          };
        }

        // 处理常规内容
        if (content) {
          return {
            delta: content,
            isComplete: false,
            metadata: {
              isReasoning: false,
              originalDelta: delta
            }
          };
        }
      }

      // Claude format
      if (parsed.delta?.text) {
        return {
          delta: parsed.delta.text,
          isComplete: false,
        };
      }

      // Generic format
      if (parsed.content || parsed.text) {
        return {
          delta: parsed.content || parsed.text,
          isComplete: false,
        };
      }

      return null;
    } catch (error) {
      console.warn('Failed to parse SSE data:', data, error);
      return null;
    }
  }
}

// JSON Lines stream processor
export class JSONLinesStreamProcessor implements StreamProcessor {
  name = 'jsonlines';

  canProcess(response: Response): boolean {
    const contentType = response.headers.get('content-type') || '';
    return contentType.includes('application/x-ndjson') ||
           contentType.includes('application/jsonlines');
  }

  async *processStream(response: Response): AsyncIterable<StreamChunk> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new AIModelError({
        type: 'NETWORK_ERROR',
        message: 'No readable stream available',
        retryable: false,
      });
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            const chunk = this.parseJSONLine(line);
            if (chunk) {
              yield chunk;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  private parseJSONLine(line: string): StreamChunk | null {
    try {
      const parsed = JSON.parse(line);
      
      if (parsed.delta !== undefined) {
        return {
          delta: String(parsed.delta),
          isComplete: parsed.isComplete || false,
          usage: parsed.usage,
          metadata: parsed.metadata,
        };
      }

      return null;
    } catch (error) {
      console.warn('Failed to parse JSON line:', line, error);
      return null;
    }
  }
}

// Stream manager for handling different stream types
export class StreamManager {
  private processors: StreamProcessor[] = [
    new SSEStreamProcessor(),
    new JSONLinesStreamProcessor(),
  ];

  addProcessor(processor: StreamProcessor): void {
    this.processors.push(processor);
  }

  async *processResponse(response: Response): AsyncIterable<StreamChunk> {
    // Find appropriate processor
    const processor = this.processors.find(p => p.canProcess(response));
    
    if (!processor) {
      throw new AIModelError({
        type: 'PARSING_ERROR',
        message: `Unsupported stream content type: ${response.headers.get('content-type')}`,
        retryable: false,
        fallbackOptions: ['disable-streaming'],
      });
    }

    console.debug(`Using stream processor: ${processor.name}`);
    yield* processor.processStream(response);
  }
}

// Stream buffer for collecting chunks
export class StreamBuffer {
  private chunks: string[] = [];
  private metadata: Record<string, any> = {};
  private totalUsage = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };

  add(chunk: StreamChunk): void {
    if (chunk.delta) {
      this.chunks.push(chunk.delta);
    }

    if (chunk.usage) {
      this.totalUsage = chunk.usage;
    }

    if (chunk.metadata) {
      Object.assign(this.metadata, chunk.metadata);
    }
  }

  getContent(): string {
    return this.chunks.join('');
  }

  getUsage() {
    return { ...this.totalUsage };
  }

  getMetadata() {
    return { ...this.metadata };
  }

  clear(): void {
    this.chunks = [];
    this.metadata = {};
    this.totalUsage = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
  }
}

// Streaming utilities
export class StreamingUtils {
  static async collectStream(stream: AsyncIterable<StreamChunk>): Promise<{
    content: string;
    usage: { promptTokens: number; completionTokens: number; totalTokens: number };
    metadata: Record<string, any>;
  }> {
    const buffer = new StreamBuffer();
    
    for await (const chunk of stream) {
      buffer.add(chunk);
    }

    return {
      content: buffer.getContent(),
      usage: buffer.getUsage(),
      metadata: buffer.getMetadata(),
    };
  }

  static async *transformStream<T>(
    stream: AsyncIterable<StreamChunk>,
    transformer: (chunk: StreamChunk) => T
  ): AsyncIterable<T> {
    for await (const chunk of stream) {
      yield transformer(chunk);
    }
  }

  static async *filterStream(
    stream: AsyncIterable<StreamChunk>,
    predicate: (chunk: StreamChunk) => boolean
  ): AsyncIterable<StreamChunk> {
    for await (const chunk of stream) {
      if (predicate(chunk)) {
        yield chunk;
      }
    }
  }

  static async *throttleStream(
    stream: AsyncIterable<StreamChunk>,
    delayMs: number
  ): AsyncIterable<StreamChunk> {
    for await (const chunk of stream) {
      yield chunk;
      if (!chunk.isComplete && delayMs > 0) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }
  }

  static createAbortableStream(
    stream: AsyncIterable<StreamChunk>,
    signal: AbortSignal
  ): AsyncIterable<StreamChunk> {
    return (async function* () {
      for await (const chunk of stream) {
        if (signal.aborted) {
          throw new AIModelError({
            type: 'TIMEOUT_ERROR',
            message: 'Stream aborted by signal',
            retryable: false,
          });
        }
        yield chunk;
      }
    })();
  }
}

// Global stream manager instance
export const streamManager = new StreamManager();